import os
from enum import Enum
from typing import List, Optional, Union

from pydantic_settings import BaseSettings


class Environment(str, Enum):
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"


class BaseConfig(BaseSettings):
    """Base configuration shared across all environments"""

    # App metadata
    app_name: str = "PLATYFEND-AI"
    version: str = "0.1.0"
    description: str = "Next generation secure code review agent"

    # Environment detection
    environment: Environment = Environment.DEVELOPMENT

    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000

    # Security
    secret_key: str = "dev-secret-key-change-in-production"

    # External APIs
    github_token: Optional[str] = None
    openai_api_key: Optional[str] = None

    # GitHub App Configuration
    github_app_id: Optional[str] = None
    github_private_key: Optional[str] = None
    github_webhook_secret: Optional[str] = None

    # GitLab Configuration
    gitlab_client_id: Optional[str] = None
    gitlab_client_secret: Optional[str] = None
    gitlab_redirect_uri: Optional[str] = None
    gitlab_secret: Optional[str] = None

    # Analysis tools configuration
    semgrep_timeout: int = 300
    ast_grep_timeout: int = 60
    linter_timeout: int = 120

    # Analysis engine settings
    max_concurrent_analyzers: int = 3
    analysis_timeout: int = 600  # Total analysis timeout

    # Comment generation settings
    openai_model: str = "gpt-4o-mini"
    comment_generation_timeout: int = 30
    max_comment_length: int = 2000

    # GitHub API settings
    github_api_timeout: int = 30
    github_max_retries: int = 3
    github_retry_delay: int = 5

    # MongoDB settings
    mongodb_uri: Optional[str] = None
    mongodb_database: str = "platyfend_ai"
    mongodb_timeout: int = 10

    # Rate limiting
    github_rate_limit_buffer: int = 100  # Keep this many requests in reserve

    # Security settings
    max_diff_size_mb: int = 10  # Maximum diff size to process
    max_files_per_pr: int = 100  # Maximum files to analyze per PR

    # Logging configuration
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_file: Optional[str] = None

    # Feature flags
    enable_ai_comments: bool = True
    enable_semgrep: bool = True
    enable_ast_grep: bool = True
    enable_linters: bool = True

    # Common settings that all environments need
    debug: bool = True
    reload: bool = True
    log_level: str = "DEBUG"

    # CORS settings - will be overridden by subclasses
    allowed_origins: List[str] = ["*"]
    allowed_methods: List[str] = ["*"]
    allowed_headers: List[str] = ["*"]

    # FastAPI docs
    docs_url: Optional[str] = "/docs"
    redoc_url: Optional[str] = "/redoc"

    # Database
    database_url: str = "sqlite:///./dev.db"

    # Timeouts
    request_timeout: int = 30

    class Config:
        env_file = ".env"
        case_sensitive = False


class DevelopmentConfig(BaseConfig):
    """Development-specific configuration"""

    # Development overrides (inherits defaults from BaseConfig)
    # CORS - permissive for development (already set in BaseConfig)
    # FastAPI docs - enabled (already set in BaseConfig)
    # All other settings use BaseConfig defaults
    pass


class ProductionConfig(BaseConfig):
    """Production-specific configuration"""

    # Security settings
    debug: bool = False
    reload: bool = False

    # Logging
    log_level: str = "INFO"

    # CORS - restrictive for production
    allowed_origins: List[str] = [
        "https://yourdomain.com",
        "https://api.yourdomain.com",
    ]
    allowed_methods: List[str] = ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: List[str] = ["Content-Type", "Authorization"]

    # Disable docs in production (optional)
    docs_url: Optional[str] = None
    redoc_url: Optional[str] = None

    # Production database
    database_url: str = "postgresql://user:pass@localhost/platyfend_prod"

    # Production MongoDB
    mongodb_uri: str = (
        "mongodb+srv://mrleritaite:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
    )

    # Timeouts - longer for production stability
    request_timeout: int = 120

    # Production-specific validations
    @property
    def secret_key(self) -> str:
        key = super().secret_key
        if key == "dev-secret-key-change-in-production":
            raise ValueError("Must set SECRET_KEY in production environment")
        return key


# Configuration factory
def get_settings() -> Union[DevelopmentConfig, ProductionConfig]:
    """Get settings based on environment variable"""
    env = os.getenv("ENVIRONMENT", "development").lower()

    if env == "production":
        return ProductionConfig()
    else:
        return DevelopmentConfig()


# Global settings instance
settings = get_settings()
