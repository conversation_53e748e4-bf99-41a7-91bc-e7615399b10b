import asyncio
import json
import logging
import shutil
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

from platyfend_ai.config.settings import settings
from platyfend_ai.core.analysis_engine import Ana<PERSON><PERSON><PERSON>rror, BaseAnalyzer
from platyfend_ai.models.analysis import (
    AnalysisResult,
    AnalyzerType,
    CodeLocation,
    SecurityFinding,
    SeverityLevel,
)

logger = logging.getLogger(__name__)


class SemgrepAnalyzer(BaseAnalyzer):
    """Semgrep security analyzer implementation"""

    def __init__(
        self, config_path: Optional[str] = None, rules: Optional[List[str]] = None
    ):
        """
        Initialize the Semgrep analyzer.

        Args:
            config_path: Path to custom Semgrep configuration file
            rules: List of specific rules to run (e.g., ['p/security-audit', 'p/owasp-top-10'])
        """
        super().__init__("Semgrep", AnalyzerType.SEMGREP, self._get_semgrep_version())

        self.config_path = config_path
        self.rules = rules or ["p/security-audit", "p/owasp-top-10", "p/cwe-top-25"]
        self.timeout = getattr(settings, "semgrep_timeout", 300)

        self.logger.info(f"Semgrep analyzer initialized with rules: {self.rules}")

    def _get_semgrep_version(self) -> Optional[str]:
        """Get Semgrep version if available"""
        try:
            import subprocess

            result = subprocess.run(
                ["semgrep", "--version"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception:
            pass
        return None

    def is_available(self) -> bool:
        """Check if Semgrep is available"""
        return shutil.which("semgrep") is not None

    def get_supported_file_extensions(self) -> List[str]:
        """Get file extensions supported by Semgrep"""
        return [
            ".py",
            ".js",
            ".ts",
            ".jsx",
            ".tsx",
            ".java",
            ".go",
            ".rb",
            ".php",
            ".c",
            ".cpp",
            ".cc",
            ".cxx",
            ".h",
            ".hpp",
            ".cs",
            ".scala",
            ".kt",
            ".swift",
            ".rs",
            ".yaml",
            ".yml",
            ".json",
            ".xml",
            ".html",
            ".htm",
        ]

    async def analyze(
        self, diff_content: str, files_info: Dict[str, Any], temp_dir: Path
    ) -> AnalysisResult:
        """
        Run Semgrep analysis on the provided diff.

        Args:
            diff_content: The diff content to analyze
            files_info: Information about changed files
            temp_dir: Temporary directory for analysis

        Returns:
            Analysis result with security findings
        """
        started_at = datetime.now(timezone.utc)

        try:
            self.logger.info("Starting Semgrep analysis")

            # Extract and write changed files to temp directory
            changed_files = await self._extract_changed_files(
                diff_content, files_info, temp_dir
            )

            if not changed_files:
                self.logger.info("No supported files found for Semgrep analysis")
                return AnalysisResult(
                    analyzer_type=self.analyzer_type,
                    analyzer_version=self.version,
                    started_at=started_at,
                    completed_at=datetime.now(timezone.utc),
                    duration_seconds=0,
                    success=True,
                    total_findings=0,
                    files_analyzed=[],
                    error_message=None,
                    raw_output="",
                )

            # Run Semgrep
            semgrep_output = await self._run_semgrep(temp_dir, changed_files)

            # Parse results
            findings = self._parse_semgrep_output(semgrep_output, temp_dir)

            completed_at = datetime.now(timezone.utc)
            duration = (completed_at - started_at).total_seconds()

            result = AnalysisResult(
                analyzer_type=self.analyzer_type,
                analyzer_version=self.version,
                started_at=started_at,
                completed_at=completed_at,
                duration_seconds=duration,
                findings=findings,
                files_analyzed=changed_files,
                success=True,
                total_findings=len(findings),
                raw_output=semgrep_output,
                error_message=None,
            )

            self.logger.info(
                f"Semgrep analysis completed: {len(findings)} findings in {duration:.2f}s"
            )
            return result

        except Exception as e:
            completed_at = datetime.now(timezone.utc)
            duration = (completed_at - started_at).total_seconds()

            self.logger.error(f"Semgrep analysis failed: {e}")

            return AnalysisResult(
                analyzer_type=self.analyzer_type,
                analyzer_version=self.version,
                started_at=started_at,
                completed_at=completed_at,
                duration_seconds=duration,
                success=False,
                error_message=str(e),
                total_findings=0,
                files_analyzed=[],
                raw_output="",
            )

    async def _extract_changed_files(
        self, _diff_content: str, files_info: Dict[str, Any], temp_dir: Path
    ) -> List[str]:
        """
        Extract changed files from diff and write them to temp directory.

        Args:
            diff_content: The diff content
            files_info: File information from GitHub API
            temp_dir: Temporary directory to write files

        Returns:
            List of file paths that were extracted
        """
        changed_files = []

        # Get list of changed files from files_info
        files = files_info.get("files", [])

        for file_info in files:
            filename = file_info.get("filename", "")
            status = file_info.get("status", "")

            # Skip deleted files
            if status == "removed":
                continue

            # Check if file is supported
            if not self.should_analyze_file(filename):
                continue

            # For now, we'll create placeholder files since we only have the diff
            # In a real implementation, you'd fetch the full file content
            file_path = temp_dir / filename
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Create a placeholder file with some content
            # This is a simplified approach - in production you'd want to reconstruct
            # the actual file content from the diff or fetch it from the repository
            file_path.write_text(
                f"# Placeholder for {filename}\n# Analysis based on diff content\n"
            )

            changed_files.append(str(file_path.relative_to(temp_dir)))

        return changed_files

    async def _run_semgrep(self, temp_dir: Path, _changed_files: List[str]) -> str:
        """
        Run Semgrep on the specified files.

        Args:
            temp_dir: Directory containing files to analyze
            changed_files: List of files to analyze

        Returns:
            Semgrep output as JSON string
        """
        cmd = ["semgrep", "--json", "--no-git-ignore", "--disable-version-check"]

        # Add rules
        if self.config_path:
            cmd.extend(["--config", self.config_path])
        else:
            for rule in self.rules:
                cmd.extend(["--config", rule])

        # Add target directory
        cmd.append(str(temp_dir))

        self.logger.debug(f"Running Semgrep command: {' '.join(cmd)}")

        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=temp_dir,
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=self.timeout
            )

            if (
                process.returncode != 0 and process.returncode != 1
            ):  # 1 is normal for findings
                error_msg = stderr.decode("utf-8") if stderr else "Unknown error"
                raise AnalyzerError(
                    f"Semgrep failed with return code {process.returncode}: {error_msg}"
                )

            return stdout.decode("utf-8")

        except asyncio.TimeoutError:
            raise AnalyzerError(
                f"Semgrep analysis timed out after {self.timeout} seconds"
            )
        except Exception as e:
            raise AnalyzerError(f"Failed to run Semgrep: {e}")

    def _parse_semgrep_output(
        self, output: str, temp_dir: Path
    ) -> List[SecurityFinding]:
        """
        Parse Semgrep JSON output into SecurityFinding objects.

        Args:
            output: Semgrep JSON output
            temp_dir: Temporary directory used for analysis

        Returns:
            List of security findings
        """
        findings = []

        try:
            data = json.loads(output)
            results = data.get("results", [])

            for result in results:
                finding = self._create_finding_from_result(result, temp_dir)
                if finding:
                    findings.append(finding)

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse Semgrep output as JSON: {e}")
            raise AnalyzerError(f"Invalid Semgrep output format: {e}")
        except Exception as e:
            self.logger.error(f"Error parsing Semgrep results: {e}")
            raise AnalyzerError(f"Failed to parse Semgrep results: {e}")

        return findings

    def _create_finding_from_result(
        self, result: Dict[str, Any], temp_dir: Path
    ) -> Optional[SecurityFinding]:
        """
        Create a SecurityFinding from a Semgrep result.

        Args:
            result: Single Semgrep result
            temp_dir: Temporary directory used for analysis

        Returns:
            SecurityFinding object or None if parsing fails
        """
        try:
            # Extract basic information
            check_id = result.get("check_id", "unknown")
            message = result.get("message", "")

            # Extract location information
            path = result.get("path", "")
            start_line = result.get("start", {}).get("line", 1)
            end_line = result.get("end", {}).get("line", start_line)
            start_col = result.get("start", {}).get("col")
            end_col = result.get("end", {}).get("col")

            # Make path relative to temp_dir
            try:
                relative_path = Path(path).relative_to(temp_dir)
                file_path = str(relative_path)
            except ValueError:
                file_path = path

            # Extract metadata
            extra = result.get("extra", {})
            severity = self._map_semgrep_severity(extra.get("severity", "INFO"))

            # Extract code snippet
            code_snippet = extra.get("lines", "")

            # Create location
            location = CodeLocation(
                file_path=file_path,
                line_start=start_line,
                line_end=end_line if end_line != start_line else None,
                column_start=start_col,
                column_end=end_col,
            )

            # Create finding
            finding = SecurityFinding(
                analyzer_type=self.analyzer_type,
                rule_id=check_id,
                rule_name=check_id.replace("-", " ").title(),
                severity=severity,
                location=location,
                title=f"Semgrep: {check_id}",
                description=message,
                message=message,
                code_snippet=code_snippet,
                metadata=extra,
                fix_suggestion=None,
                confidence=None,
            )

            return finding

        except Exception as e:
            self.logger.warning(f"Failed to parse Semgrep result: {e}")
            return None

    def _map_semgrep_severity(self, semgrep_severity: str) -> SeverityLevel:
        """
        Map Semgrep severity to our SeverityLevel enum.

        Args:
            semgrep_severity: Semgrep severity string

        Returns:
            Mapped severity level
        """
        severity_map = {
            "ERROR": SeverityLevel.HIGH,
            "WARNING": SeverityLevel.MEDIUM,
            "INFO": SeverityLevel.LOW,
        }

        return severity_map.get(semgrep_severity.upper(), SeverityLevel.INFO)
