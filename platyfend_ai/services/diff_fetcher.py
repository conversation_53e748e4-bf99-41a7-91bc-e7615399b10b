import asyncio
import logging
from typing import Any, Dict, Optional
from urllib.parse import urlparse

import httpx
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from platyfend_ai.config.settings import settings

logger = logging.getLogger(__name__)


class DiffFetchError(Exception):
    """Exception raised when diff fetching fails"""

    pass


class RateLimitError(Exception):
    """Exception raised when rate limit is exceeded"""

    pass


class DiffFetcher:
    """Service for fetching PR diffs and patches from GitHub"""

    def __init__(self, github_token: Optional[str] = None):
        """
        Initialize the diff fetcher.

        Args:
            github_token: GitHub personal access token for authentication
        """
        self.github_token = github_token or settings.github_token
        self.base_headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": f"{settings.app_name}/{settings.version}",
        }

        if self.github_token:
            self.base_headers["Authorization"] = f"token {self.github_token}"

        # HTTP client configuration
        self.timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=5.0)

        logger.info(
            "DiffFetcher initialized with authentication"
            if self.github_token
            else "DiffFetcher initialized without authentication"
        )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, RateLimitError)),
    )
    async def _make_request(
        self, url: str, headers: Optional[Dict[str, str]] = None
    ) -> httpx.Response:
        """
        Make an HTTP request with retry logic and rate limiting handling.

        Args:
            url: URL to fetch
            headers: Additional headers to include

        Returns:
            HTTP response

        Raises:
            DiffFetchError: If the request fails after retries
            RateLimitError: If rate limit is exceeded
        """
        request_headers = self.base_headers.copy()
        if headers:
            request_headers.update(headers)

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.debug(f"Making request to: {url}")
                response = await client.get(url, headers=request_headers)

                # Handle rate limiting
                if (
                    response.status_code == 403
                    and "rate limit" in response.text.lower()
                ):
                    reset_time = response.headers.get("X-RateLimit-Reset")
                    logger.warning(f"Rate limit exceeded. Reset time: {reset_time}")
                    raise RateLimitError(
                        f"GitHub API rate limit exceeded. Reset time: {reset_time}"
                    )

                # Handle other client errors
                if response.status_code == 401:
                    raise DiffFetchError("Authentication failed. Check GitHub token.")
                elif response.status_code == 404:
                    raise DiffFetchError(
                        "Resource not found. Check URL and permissions."
                    )
                elif response.status_code >= 400:
                    raise DiffFetchError(
                        f"HTTP {response.status_code}: {response.text}"
                    )

                response.raise_for_status()
                return response

            except httpx.RequestError as e:
                logger.error(f"Request error for {url}: {e}")
                raise DiffFetchError(f"Network error: {e}")

    async def fetch_diff(self, diff_url: str) -> str:
        """
        Fetch the diff content from a GitHub diff URL.

        Args:
            diff_url: URL to the diff (e.g., from PR webhook data)

        Returns:
            Diff content as string

        Raises:
            DiffFetchError: If fetching fails
        """
        try:
            logger.info(f"Fetching diff from: {diff_url}")

            # Validate URL
            parsed_url = urlparse(diff_url)
            if not parsed_url.netloc or "github.com" not in parsed_url.netloc:
                raise DiffFetchError(f"Invalid GitHub URL: {diff_url}")

            # Set appropriate headers for diff format
            headers = {
                "Accept": "application/vnd.github.v3.diff",
                "Authorization": f"Bearer {self.github_token}",
            }

            response = await self._make_request(diff_url, headers)
            diff_content = response.text

            logger.info(f"Successfully fetched diff ({len(diff_content)} characters)")
            return diff_content

        except Exception as e:
            logger.error(f"Failed to fetch diff from {diff_url}: {e}")
            raise DiffFetchError(f"Failed to fetch diff: {e}")

    async def fetch_patch(self, patch_url: str) -> str:
        """
        Fetch the patch content from a GitHub patch URL.

        Args:
            patch_url: URL to the patch (e.g., from PR webhook data)

        Returns:
            Patch content as string

        Raises:
            DiffFetchError: If fetching fails
        """
        try:
            logger.info(f"Fetching patch from: {patch_url}")

            # Validate URL
            parsed_url = urlparse(patch_url)
            if not parsed_url.netloc or "github.com" not in parsed_url.netloc:
                raise DiffFetchError(f"Invalid GitHub URL: {patch_url}")

            # Set appropriate headers for patch format
            headers = {"Accept": "application/vnd.github.v3.patch"}

            response = await self._make_request(patch_url, headers)
            patch_content = response.text

            logger.info(f"Successfully fetched patch ({len(patch_content)} characters)")
            return patch_content

        except Exception as e:
            logger.error(f"Failed to fetch patch from {patch_url}: {e}")
            raise DiffFetchError(f"Failed to fetch patch: {e}")

    async def fetch_pr_files(self, repository: str, pr_number: int) -> Dict[str, Any]:
        """
        Fetch the list of files changed in a PR using GitHub API.

        Args:
            repository: Repository name in format "owner/repo"
            pr_number: Pull request number

        Returns:
            Dictionary containing file information

        Raises:
            DiffFetchError: If fetching fails
        """
        try:
            url = f"https://api.github.com/repos/{repository}/pulls/{pr_number}/files"
            logger.info(f"Fetching PR files from: {url}")

            response = await self._make_request(url)
            files_data = response.json()

            logger.info(
                f"Successfully fetched {len(files_data)} file(s) for PR #{pr_number}"
            )
            return {
                "files": files_data,
                "total_files": len(files_data),
                "additions": sum(file.get("additions", 0) for file in files_data),
                "deletions": sum(file.get("deletions", 0) for file in files_data),
                "changes": sum(file.get("changes", 0) for file in files_data),
            }

        except Exception as e:
            logger.error(f"Failed to fetch PR files for {repository}#{pr_number}: {e}")
            raise DiffFetchError(f"Failed to fetch PR files: {e}")

    async def fetch_pr_diff_and_files(
        self, diff_url: str, repository: str, pr_number: int
    ) -> Dict[str, Any]:
        """
        Fetch both diff content and file information for a PR.

        Args:
            diff_url: URL to the diff
            repository: Repository name in format "owner/repo"
            pr_number: Pull request number

        Returns:
            Dictionary containing both diff content and file information

        Raises:
            DiffFetchError: If fetching fails
        """
        try:
            logger.info(f"Fetching complete PR data for {repository}#{pr_number}")

            # Fetch both diff and files concurrently
            diff_task = self.fetch_diff(diff_url)
            files_task = self.fetch_pr_files(repository, pr_number)

            diff_content, files_info = await asyncio.gather(diff_task, files_task)

            result = {
                "diff_content": diff_content,
                "files_info": files_info,
                "repository": repository,
                "pr_number": pr_number,
            }

            logger.info(
                f"Successfully fetched complete PR data for {repository}#{pr_number}"
            )
            return result

        except Exception as e:
            logger.error(
                f"Failed to fetch complete PR data for {repository}#{pr_number}: {e}"
            )
            raise DiffFetchError(f"Failed to fetch complete PR data: {e}")

    def __del__(self):
        """Cleanup when the object is destroyed"""
        logger.debug("DiffFetcher instance destroyed")


# Convenience function for quick access
async def fetch_pr_diff(diff_url: str, github_token: Optional[str] = None) -> str:
    """
    Convenience function to fetch a PR diff.

    Args:
        diff_url: URL to the diff
        github_token: Optional GitHub token for authentication

    Returns:
        Diff content as string
    """
    fetcher = DiffFetcher(github_token)
    return await fetcher.fetch_diff(diff_url)


async def fetch_pr_patch(patch_url: str, github_token: Optional[str] = None) -> str:
    """
    Convenience function to fetch a PR patch.

    Args:
        patch_url: URL to the patch
        github_token: Optional GitHub token for authentication

    Returns:
        Patch content as string
    """
    fetcher = DiffFetcher(github_token)
    return await fetcher.fetch_patch(patch_url)
