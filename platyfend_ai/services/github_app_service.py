"""GitHub App service for generating installation tokens."""

import logging
import time
from typing import Dict, Optional

import httpx
import jwt
from tenacity import retry, stop_after_attempt, wait_exponential

from platyfend_ai.config.settings import settings

logger = logging.getLogger(__name__)


class GitHubAppTokenError(Exception):
    """Exception raised when GitHub App token operations fail."""
    pass


class GitHubAppService:
    """Service for GitHub App authentication and token generation."""

    def __init__(
        self,
        app_id: Optional[str] = None,
        private_key: Optional[str] = None,
    ):
        """
        Initialize GitHub App service.

        Args:
            app_id: GitHub App ID
            private_key: GitHub App private key (PEM format)
        """
        self.app_id = app_id or settings.github_app_id
        self.private_key = private_key or settings.github_private_key

        if not self.app_id:
            raise GitHubAppTokenError("GitHub App ID is required")
        if not self.private_key:
            raise GitHubAppTokenError("GitHub App private key is required")

        # HTTP client configuration
        self.timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=5.0)
        
        logger.info(f"GitHub App service initialized with App ID: {self.app_id}")

    def _generate_jwt_token(self) -> str:
        """
        Generate a JWT token for GitHub App authentication.

        Returns:
            JWT token string

        Raises:
            GitHubAppTokenError: If JWT generation fails
        """
        try:
            now = int(time.time())
            payload = {
                "iat": now - 60,  # Issued at time (60 seconds ago to account for clock skew)
                "exp": now + 600,  # Expires in 10 minutes (GitHub's maximum)
                "iss": int(self.app_id),  # Issuer (GitHub App ID)
            }

            # Generate JWT token
            jwt_token = jwt.encode(payload, self.private_key, algorithm="RS256")
            
            # Ensure it's a string (PyJWT sometimes returns bytes)
            if isinstance(jwt_token, bytes):
                jwt_token = jwt_token.decode("utf-8")

            logger.debug("Generated JWT token for GitHub App authentication")
            return jwt_token

        except Exception as e:
            logger.error(f"Failed to generate JWT token: {e}")
            raise GitHubAppTokenError(f"JWT generation failed: {e}")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True,
    )
    async def _make_github_request(self, url: str, headers: Dict[str, str], method: str = "POST") -> httpx.Response:
        """
        Make an authenticated request to GitHub API.

        Args:
            url: GitHub API URL
            headers: Request headers
            method: HTTP method

        Returns:
            HTTP response

        Raises:
            GitHubAppTokenError: If request fails
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() == "POST":
                    response = await client.post(url, headers=headers)
                else:
                    response = await client.get(url, headers=headers)

                # Handle rate limiting
                if response.status_code == 403 and "rate limit" in response.text.lower():
                    reset_time = response.headers.get("X-RateLimit-Reset")
                    if reset_time:
                        wait_time = int(reset_time) - int(time.time())
                        logger.warning(f"Rate limited. Reset in {wait_time} seconds")
                    raise GitHubAppTokenError("GitHub API rate limit exceeded")

                # Handle authentication errors
                if response.status_code == 401:
                    raise GitHubAppTokenError("GitHub App authentication failed")

                # Handle not found errors
                if response.status_code == 404:
                    raise GitHubAppTokenError("GitHub App installation not found")

                # Handle other client errors
                if response.status_code >= 400:
                    error_msg = f"GitHub API error {response.status_code}: {response.text}"
                    logger.error(error_msg)
                    raise GitHubAppTokenError(error_msg)

                response.raise_for_status()
                return response

        except httpx.RequestError as e:
            logger.error(f"Request error for {url}: {e}")
            raise GitHubAppTokenError(f"Network error: {e}")

    async def generate_installation_token(self, installation_id: int) -> Dict[str, str]:
        """
        Generate an installation access token for a GitHub App installation.

        Args:
            installation_id: GitHub App installation ID

        Returns:
            Dictionary containing token and expiration info

        Raises:
            GitHubAppTokenError: If token generation fails
        """
        try:
            logger.info(f"Generating installation token for installation ID: {installation_id}")

            # Generate JWT token for authentication
            jwt_token = self._generate_jwt_token()

            # Prepare request to GitHub API
            url = f"https://api.github.com/app/installations/{installation_id}/access_tokens"
            headers = {
                "Authorization": f"Bearer {jwt_token}",
                "Accept": "application/vnd.github+json",
                "User-Agent": f"{settings.app_name}/{settings.version}",
            }

            # Make request to generate installation token
            response = await self._make_github_request(url, headers, method="POST")
            token_data = response.json()

            # Extract token information
            access_token = token_data.get("token")
            expires_at = token_data.get("expires_at")

            if not access_token:
                raise GitHubAppTokenError("No access token in GitHub response")

            logger.info(f"Successfully generated installation token for installation ID: {installation_id}")
            
            return {
                "token": access_token,
                "expires_at": expires_at,
                "installation_id": str(installation_id),
            }

        except GitHubAppTokenError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error generating installation token for {installation_id}: {e}")
            raise GitHubAppTokenError(f"Token generation failed: {e}")

    async def get_installation_info(self, installation_id: int) -> Dict:
        """
        Get information about a GitHub App installation.

        Args:
            installation_id: GitHub App installation ID

        Returns:
            Installation information

        Raises:
            GitHubAppTokenError: If request fails
        """
        try:
            logger.info(f"Getting installation info for installation ID: {installation_id}")

            # Generate JWT token for authentication
            jwt_token = self._generate_jwt_token()

            # Prepare request to GitHub API
            url = f"https://api.github.com/app/installations/{installation_id}"
            headers = {
                "Authorization": f"Bearer {jwt_token}",
                "Accept": "application/vnd.github+json",
                "User-Agent": f"{settings.app_name}/{settings.version}",
            }

            # Make request to get installation info
            response = await self._make_github_request(url, headers, method="GET")
            installation_data = response.json()

            logger.info(f"Successfully retrieved installation info for installation ID: {installation_id}")
            return installation_data

        except GitHubAppTokenError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting installation info for {installation_id}: {e}")
            raise GitHubAppTokenError(f"Failed to get installation info: {e}")


# Convenience function for quick token generation
async def generate_github_app_token(installation_id: int) -> Dict[str, str]:
    """
    Convenience function to generate a GitHub App installation token.

    Args:
        installation_id: GitHub App installation ID

    Returns:
        Dictionary containing token and expiration info
    """
    service = GitHubAppService()
    return await service.generate_installation_token(installation_id)
